using Core.Entities;
using Core.Utilities.Security.CompanyContext;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Core.DataAccess.EntityFramework
{
    public class EfCompanyEntityRepositoryBase<TEntity, TContext> : EfEntityRepositoryBase<TEntity, TContext>, IEntityRepository<TEntity>
        where TEntity : class, ICompanyEntity, new()
        where TContext : DbContext
    {
        private readonly ICompanyContext _companyContext;

        // Enterprise Constructor Injection Pattern
        public EfCompanyEntityRepositoryBase(TContext context, ICompanyContext companyContext) : base(context)
        {
            _companyContext = companyContext ?? throw new ArgumentNullException(nameof(companyContext));
        }

        // SYNC METHODS (Multi-tenant aware)
        public override List<TEntity> GetAll(Expression<Func<TEntity, bool>> filter = null)
        {
            try
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Eğer şirket ID'si geçerli değilse, boş liste döndür
                if (companyId <= 0)
                {
                    return new List<TEntity>();
                }

                // Şirket ID'sine göre filtrele
                var query = _context.Set<TEntity>().Where(e => e.CompanyID == companyId);

                // Eğer ek filtre varsa, uygula
                if (filter != null)
                {
                    query = query.Where(filter);
                }

                return query.ToList();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Multi-tenant entity listesi getirilirken hata oluştu: {ex.Message}", ex);
            }
        }

        public override TEntity Get(Expression<Func<TEntity, bool>> filter)
        {
            try
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Eğer şirket ID'si geçerli değilse, null döndür
                if (companyId <= 0)
                {
                    return null;
                }

                // Şirket ID'sine göre filtrele ve ek filtreyi uygula
                return _context.Set<TEntity>()
                    .Where(e => e.CompanyID == companyId)
                    .SingleOrDefault(filter);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Multi-tenant entity getirilirken hata oluştu: {ex.Message}", ex);
            }
        }

        public override void Add(TEntity entity)
        {
            try
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Eğer şirket ID'si geçerli değilse, işlemi iptal et
                if (companyId <= 0)
                {
                    throw new UnauthorizedAccessException("Geçerli bir şirket ID'si bulunamadı. Multi-tenant güvenlik ihlali!");
                }

                // Varlığın şirket ID'sini ayarla
                entity.CompanyID = companyId;

                // Temel sınıfın Add metodunu çağır
                base.Add(entity);
            }
            catch (UnauthorizedAccessException)
            {
                throw; // Multi-tenant güvenlik hatalarını yeniden fırlat
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Multi-tenant entity eklenirken hata oluştu: {ex.Message}", ex);
            }
        }

        public override void Update(TEntity entity)
        {
            try
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Eğer şirket ID'si geçerli değilse, işlemi iptal et
                if (companyId <= 0)
                {
                    throw new UnauthorizedAccessException("Geçerli bir şirket ID'si bulunamadı. Multi-tenant güvenlik ihlali!");
                }

                // Varlığın şirket ID'sini ayarla
                entity.CompanyID = companyId;

                // Temel sınıfın Update metodunu çağır
                base.Update(entity);
            }
            catch (UnauthorizedAccessException)
            {
                throw; // Multi-tenant güvenlik hatalarını yeniden fırlat
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Multi-tenant entity güncellenirken hata oluştu: {ex.Message}", ex);
            }
        }

        // ASYNC METHODS (Multi-tenant aware)
        public override async Task<List<TEntity>> GetAllAsync(Expression<Func<TEntity, bool>> filter = null)
        {
            try
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Eğer şirket ID'si geçerli değilse, boş liste döndür
                if (companyId <= 0)
                {
                    return new List<TEntity>();
                }

                // Şirket ID'sine göre filtrele
                var query = _context.Set<TEntity>().Where(e => e.CompanyID == companyId);

                // Eğer ek filtre varsa, uygula
                if (filter != null)
                {
                    query = query.Where(filter);
                }

                return await query.ToListAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Multi-tenant entity listesi async getirilirken hata oluştu: {ex.Message}", ex);
            }
        }

        public override async Task<TEntity> GetAsync(Expression<Func<TEntity, bool>> filter)
        {
            try
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Eğer şirket ID'si geçerli değilse, null döndür
                if (companyId <= 0)
                {
                    return null;
                }

                // Şirket ID'sine göre filtrele ve ek filtreyi uygula
                return await _context.Set<TEntity>()
                    .Where(e => e.CompanyID == companyId)
                    .SingleOrDefaultAsync(filter);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Multi-tenant entity async getirilirken hata oluştu: {ex.Message}", ex);
            }
        }

        public override async Task AddAsync(TEntity entity)
        {
            try
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Eğer şirket ID'si geçerli değilse, işlemi iptal et
                if (companyId <= 0)
                {
                    throw new UnauthorizedAccessException("Geçerli bir şirket ID'si bulunamadı. Multi-tenant güvenlik ihlali!");
                }

                // Varlığın şirket ID'sini ayarla
                entity.CompanyID = companyId;

                // Temel sınıfın AddAsync metodunu çağır
                await base.AddAsync(entity);
            }
            catch (UnauthorizedAccessException)
            {
                throw; // Multi-tenant güvenlik hatalarını yeniden fırlat
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Multi-tenant entity async eklenirken hata oluştu: {ex.Message}", ex);
            }
        }

        public override async Task UpdateAsync(TEntity entity)
        {
            try
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Eğer şirket ID'si geçerli değilse, işlemi iptal et
                if (companyId <= 0)
                {
                    throw new UnauthorizedAccessException("Geçerli bir şirket ID'si bulunamadı. Multi-tenant güvenlik ihlali!");
                }

                // Varlığın şirket ID'sini ayarla
                entity.CompanyID = companyId;

                // Temel sınıfın UpdateAsync metodunu çağır
                await base.UpdateAsync(entity);
            }
            catch (UnauthorizedAccessException)
            {
                throw; // Multi-tenant güvenlik hatalarını yeniden fırlat
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Multi-tenant entity async güncellenirken hata oluştu: {ex.Message}", ex);
            }
        }
    }
}
