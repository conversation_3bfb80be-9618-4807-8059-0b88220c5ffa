﻿using Core.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Core.DataAccess.EntityFramework
{
    public class EfEntityRepositoryBase<TEntity, TContext> : IEntityRepository<TEntity>
        where TEntity : class, IEntity, new()
        where TContext : DbContext
    {
        protected readonly TContext _context;

        // Enterprise Constructor Injection Pattern
        public EfEntityRepositoryBase(TContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        // SYNC METHODS (Backward Compatibility)
        public virtual void Add(TEntity entity)
        {
            try
            {
                var addedEntity = _context.Entry(entity);
                addedEntity.State = EntityState.Added;

                // Enterprise Audit Fields Management
                SetAuditFieldsForAdd(addedEntity);

                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity eklenirken hata oluştu: {ex.Message}", ex);
            }
        }

        public virtual void Delete(object id)
        {
            try
            {
                TEntity entity = _context.Set<TEntity>().Find(id);
                if (entity == null) return;

                var deletedEntity = _context.Entry(entity);
                deletedEntity.State = EntityState.Modified;

                // Enterprise Soft Delete Management
                SetAuditFieldsForDelete(deletedEntity);

                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity silinirken hata oluştu: {ex.Message}", ex);
            }
        }

        public virtual void HardDelete(object id)
        {
            try
            {
                TEntity entity = _context.Set<TEntity>().Find(id);
                if (entity == null) return;

                var deletedEntity = _context.Entry(entity);
                deletedEntity.State = EntityState.Deleted;

                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity kalıcı silinirken hata oluştu: {ex.Message}", ex);
            }
        }

        public virtual TEntity Get(Expression<Func<TEntity, bool>> filter)
        {
            try
            {
                return _context.Set<TEntity>().SingleOrDefault(filter);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity getirilirken hata oluştu: {ex.Message}", ex);
            }
        }

        public virtual List<TEntity> GetAll(Expression<Func<TEntity, bool>> filter = null)
        {
            try
            {
                return filter == null
                    ? _context.Set<TEntity>().ToList()
                    : _context.Set<TEntity>().Where(filter).ToList();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity listesi getirilirken hata oluştu: {ex.Message}", ex);
            }
        }

        public virtual void Update(TEntity entity)
        {
            try
            {
                var updatedEntity = _context.Entry(entity);
                updatedEntity.State = EntityState.Modified;

                // Enterprise Audit Fields Management
                SetAuditFieldsForUpdate(updatedEntity);

                _context.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity güncellenirken hata oluştu: {ex.Message}", ex);
            }
        }

        // ASYNC METHODS (Enterprise Performance)
        public virtual async Task<List<TEntity>> GetAllAsync(Expression<Func<TEntity, bool>> filter = null)
        {
            try
            {
                return filter == null
                    ? await _context.Set<TEntity>().ToListAsync()
                    : await _context.Set<TEntity>().Where(filter).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity listesi async getirilirken hata oluştu: {ex.Message}", ex);
            }
        }

        public virtual async Task<TEntity> GetAsync(Expression<Func<TEntity, bool>> filter)
        {
            try
            {
                return await _context.Set<TEntity>().SingleOrDefaultAsync(filter);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity async getirilirken hata oluştu: {ex.Message}", ex);
            }
        }

        public virtual async Task AddAsync(TEntity entity)
        {
            try
            {
                var addedEntity = _context.Entry(entity);
                addedEntity.State = EntityState.Added;

                // Enterprise Audit Fields Management
                SetAuditFieldsForAdd(addedEntity);

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity async eklenirken hata oluştu: {ex.Message}", ex);
            }
        }

        public virtual async Task UpdateAsync(TEntity entity)
        {
            try
            {
                var updatedEntity = _context.Entry(entity);
                updatedEntity.State = EntityState.Modified;

                // Enterprise Audit Fields Management
                SetAuditFieldsForUpdate(updatedEntity);

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity async güncellenirken hata oluştu: {ex.Message}", ex);
            }
        }

        public virtual async Task DeleteAsync(object id)
        {
            try
            {
                TEntity entity = await _context.Set<TEntity>().FindAsync(id);
                if (entity == null) return;

                var deletedEntity = _context.Entry(entity);
                deletedEntity.State = EntityState.Modified;

                // Enterprise Soft Delete Management
                SetAuditFieldsForDelete(deletedEntity);

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity async silinirken hata oluştu: {ex.Message}", ex);
            }
        }

        public virtual async Task HardDeleteAsync(object id)
        {
            try
            {
                TEntity entity = await _context.Set<TEntity>().FindAsync(id);
                if (entity == null) return;

                var deletedEntity = _context.Entry(entity);
                deletedEntity.State = EntityState.Deleted;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Entity async kalıcı silinirken hata oluştu: {ex.Message}", ex);
            }
        }

        // ENTERPRISE AUDIT FIELDS MANAGEMENT
        private void SetAuditFieldsForAdd(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry addedEntity)
        {
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                addedEntity.Property("CreationDate").CurrentValue = DateTime.Now;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                addedEntity.Property("DeletedDate").IsModified = false;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                addedEntity.Property("UpdatedDate").IsModified = false;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
            {
                addedEntity.Property("IsActive").CurrentValue = true;
            }
        }

        private void SetAuditFieldsForUpdate(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry updatedEntity)
        {
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                updatedEntity.Property("CreationDate").IsModified = false;
            }
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                updatedEntity.Property("DeletedDate").IsModified = false;
            }
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                updatedEntity.Property("UpdatedDate").CurrentValue = DateTime.Now;
            }
        }

        private void SetAuditFieldsForDelete(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry deletedEntity)
        {
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                deletedEntity.Property("CreationDate").IsModified = false;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                deletedEntity.Property("DeletedDate").CurrentValue = DateTime.Now;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                deletedEntity.Property("UpdatedDate").IsModified = false;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
            {
                deletedEntity.Property("IsActive").CurrentValue = false;
            }
        }
    }
}
