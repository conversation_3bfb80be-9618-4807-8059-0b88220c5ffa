using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfEntryExitHistoryDal : EfCompanyEntityRepositoryBase<EntryExitHistory, GymContext>, IEntryExitHistoryDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Enterprise Constructor Injection Pattern
        public EfEntryExitHistoryDal(GymContext context, Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(context, companyContext)
        {
            _companyContext = companyContext;
        }
    }
}
