﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfLicenseTransactionDal : EfEntityRepositoryBase<LicenseTransaction, GymContext>, ILicenseTransactionDal
    {
        // Enterprise Constructor Injection Pattern
        public EfLicenseTransactionDal(GymContext context) : base(context)
        {
        }
    }

}
